<?php

namespace MediaWiki\Extension\Wikisource\HookHandler;

use MediaWiki\Hook\BeforePageDisplayHook;
use MediaWiki\Output\OutputPage;
use MediaWiki\Skin\SkinTemplate;
use MediaWiki\User\UserGroupManager;
use ProofreadPage\ProofreadPageInit;
/**
 * Hook handler for the Index namespace
 */
class IndexNamespaceHandler implements BeforePageDisplayHook {
	
  /*  UserGroupManager service for checking user permissions */
	private UserGroupManager $userGroupManager;
	public function __construct( UserGroupManager $userGroupManager ) {
		$this->userGroupManager = $userGroupManager;
	}

	/**
	 * Add modules to Index namespace pages
	 *
	 * @param OutputPage $out
	 * @param SkinTemplate $skin
	 * @return void
	 */
	public function onBeforePageDisplay( $out, $skin ): void {
		$title = $out->getTitle();
		
		// Only add the module to Index namespace pages for admin users
		if ( $title->getNamespace() === ProofreadPageInit::getNamespaceId( 'index' ) ) {
			$user = $out->getUser();
			$userGroups = $this->userGroupManager->getUserGroups( $user );
			if ( in_array( 'sysop', $userGroups ) ) {
				$out->addModules( [ 'ext.wikisource.bulkocr' ] );
			}
		}
	}
}