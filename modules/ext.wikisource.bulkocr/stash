/**
	 * Get UnTranscribed pages from Current Index namespace
	 *
	 * @param {string} indexTitle - The title of the index page
	 * @return {jQuery.Promise} - Promise resolving to array of untranscribed page titles
	 */
	getUnTranscribedPagesInIndex( indexTitle ) {
		const deferred = $.Deferred();

		this.mwApi.get( {
			action: 'query',
			list: 'proofreadpagesinindex',
			prppiititle: indexTitle,
			prppiiprop: 'ids|title|formattedpagenumber',
			formatversion: 2
		} ).done( ( response ) => {
			const titlesArray = [];

			if ( response.query.proofreadpagesinindex ) {
				// Collect uncreated pages (pageid === 0)
				response.query.proofreadpagesinindex.forEach( page => {
					if ( page.pageid === 0 ) {
						titlesArray.push( page.title );
					}
				} );
			}

			deferred.resolve( titlesArray );
		} ).fail( ( xhr, status, error ) => {
			mw.notify( 'Failed to Fetch UnTranscribed Pages from Index', { type: 'error' } );
			deferred.reject( error || 'API request failed' );
		} );

		return deferred.promise();
	}
